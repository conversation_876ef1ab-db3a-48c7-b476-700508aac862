{"name": "lovebook-v2", "version": "0.1.0", "private": true, "scripts": {"prepare": "lefthook install", "dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "biome check .", "format": "biome format . --write", "typecheck": "tsc --noEmit", "prisma:generate": "prisma generate", "prisma:generate:test": "prisma generate --schema=./prisma/schema.prisma --output=./tests/generated/prisma", "prisma:migrate:dev": "prisma migrate dev", "prisma:migrate:deploy": "prisma migrate deploy", "prisma:studio": "prisma studio", "prisma:reset": "prisma migrate reset --force", "db:seed": "ts-node prisma/seed.ts", "test:setup": "docker-compose -f docker-compose.test.yml up -d && sleep 5 && npm run prisma:generate:test && dotenv -e .env.test -- npx prisma migrate deploy", "test:teardown": "docker-compose -f docker-compose.test.yml down -v", "test:server": "dotenv -e .env.test -- next dev --port 3001", "test:e2e": "npm run test:setup && npx playwright test && npm run test:teardown", "test:cucumber": "npx cucumber-js --config tests/e2e/config/cucumber.config.js tests/e2e/features", "test": "npm run test:setup && npm run test:cucumber && npm run test:teardown"}, "dependencies": {"@heroui/react": "^2.7.11", "@prisma/client": "^6.11.1", "bcrypt": "^6.0.0", "framer-motion": "^12.23.0", "jsonwebtoken": "^9.0.2", "next": "15.3.4", "nodemailer": "^7.0.5", "prisma": "^6.11.1", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@arkweid/lefthook": "^0.7.7", "@biomejs/biome": "2.1.1", "@cucumber/cucumber": "^11.3.0", "@cucumber/pretty-formatter": "^1.0.1", "@eslint/eslintrc": "^3", "@playwright/test": "^1.53.2", "@tailwindcss/typography": "^0.5.16", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^20", "@types/nodemailer": "^6.4.17", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "cross-env": "^7.0.3", "dotenv-cli": "^8.0.0", "eslint-config-next": "15.3.4", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "ts-node": "^10.9.2", "typescript": "^5"}}