#!/usr/bin/env ts-node

import { execSync } from 'child_process';
import { config } from 'dotenv';

// Load test environment variables
config({ path: '.env.test' });

async function setupTestEnvironment() {
  console.log('🚀 Setting up test environment...');

  try {
    // Start test services
    console.log('📦 Starting test services (PostgreSQL, MailHog)...');
    execSync('docker-compose -f docker-compose.test.yml up -d', { stdio: 'inherit' });

    // Wait for services to be ready
    console.log('⏳ Waiting for services to be ready...');
    await new Promise(resolve => setTimeout(resolve, 10000));

    // Run database migrations
    console.log('🗄️ Running database migrations...');
    execSync('dotenv -e .env.test -- npx prisma migrate deploy', { stdio: 'inherit' });

    console.log('✅ Test environment setup complete!');
    console.log('');
    console.log('Test services running:');
    console.log('- PostgreSQL: localhost:5433');
    console.log('- MailHog UI: http://localhost:8026');
    console.log('- MailHog SMTP: localhost:1026');
    console.log('');
    console.log('To run tests:');
    console.log('- E2E tests: npm run test:e2e');
    console.log('- Cucumber tests: npm run test:cucumber');
    console.log('- Development mode: npm run test:cucumber:dev');
    console.log('');
    console.log('To teardown: npm run test:teardown');

  } catch (error) {
    console.error('❌ Failed to setup test environment:', error);
    process.exit(1);
  }
}

async function teardownTestEnvironment() {
  console.log('🧹 Tearing down test environment...');

  try {
    execSync('docker-compose -f docker-compose.test.yml down -v', { stdio: 'inherit' });
    console.log('✅ Test environment teardown complete!');
  } catch (error) {
    console.error('❌ Failed to teardown test environment:', error);
    process.exit(1);
  }
}

// Check command line arguments
const command = process.argv[2];

if (command === 'setup') {
  setupTestEnvironment();
} else if (command === 'teardown') {
  teardownTestEnvironment();
} else {
  console.log('Usage: ts-node tests/setup-test-env.ts [setup|teardown]');
  process.exit(1);
}
