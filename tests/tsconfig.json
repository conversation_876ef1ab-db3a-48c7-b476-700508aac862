{"extends": "../tsconfig.json", "compilerOptions": {"types": ["node", "@types/node", "@playwright/test", "@cucumber/cucumber"], "moduleResolution": "node", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "allowImportingTsExtensions": true, "noEmit": true}, "include": ["**/*.ts"], "exclude": ["node_modules"], "ts-node": {"esm": true, "compilerOptions": {"module": "ESNext", "moduleResolution": "node"}}}