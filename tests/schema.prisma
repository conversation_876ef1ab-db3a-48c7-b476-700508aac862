// Test Prisma schema file
// This is a copy of the main schema with test-specific output path

generator client {
  provider = "prisma-client-js"
  output   = "./generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Otp {
  id        String   @id @default(cuid())
  email     String
  code      String
  expiresAt DateTime
}

model User {
  id    String @id @default(cuid())
  email String @unique
}
