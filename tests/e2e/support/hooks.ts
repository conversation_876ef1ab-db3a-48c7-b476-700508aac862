import { Before, After, BeforeAll, AfterAll } from "@cucumber/cucumber";
import { ICustomWorld } from "./world";
import { DatabaseHelper } from "./database-helper";
import { MailHelper } from "./mail-helper";
import { spawn, ChildProcess } from "child_process";

// Load test environment variables - override any existing env vars
require("dotenv").config({ path: ".env.test", override: true });

let databaseHelper: DatabaseHelper;
let mailHelper: MailHelper;

BeforeAll(async function () {
  // Initialize database helper with test environment
  databaseHelper = new DatabaseHelper();
  await databaseHelper.connect();

  // Initialize mail helper
  mailHelper = new MailHelper();

  console.log("🚀 Test environment initialized");
  console.log(`📊 Using database: ${process.env.DATABASE_URL}`);
});

AfterAll(async function () {
  // Cleanup database connection
  if (databaseHelper) {
    await databaseHelper.disconnect();
  }

  console.log("🧹 Test environment cleaned up");
});

Before(async function (this: ICustomWorld) {
  // Initialize browser and page objects
  await this.init();

  // Clean database before each scenario
  await databaseHelper.cleanDatabase();

  // Clear any existing emails
  await mailHelper.clearEmails();

  console.log(`🎬 Starting scenario: ${this.pickle?.name}`);
});

After(async function (this: ICustomWorld, scenario) {
  // Take screenshot on failure
  if (scenario.result?.status === "FAILED" && this.page) {
    const screenshot = await this.page.screenshot({
      path: `test-results/screenshots/${scenario.pickle?.name}-${Date.now()}.png`,
      fullPage: true,
    });
    this.attach(screenshot, "image/png");
  }

  // Cleanup browser
  await this.cleanup();

  console.log(
    `🏁 Finished scenario: ${this.pickle?.name} - ${scenario.result?.status}`,
  );
});

// Export helpers for use in step definitions
export { databaseHelper, mailHelper };
