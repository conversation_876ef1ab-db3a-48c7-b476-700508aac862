import { type Page, type Locator, expect } from "@playwright/test";

export class DashboardPage {
  readonly page: Page;
  readonly baseUrl: string;
  readonly welcomeTitle: Locator;
  readonly userEmail: Locator;
  readonly signOutButton: Locator;
  readonly avatar: Locator;

  constructor(page: Page, baseUrl: string) {
    this.page = page;
    this.baseUrl = baseUrl;
    this.welcomeTitle = page.locator('h1:has-text("Добро пожаловать")');
    this.userEmail = page.locator("text=/Вошли как .+/");
    this.signOutButton = page.locator('button:has-text("Выйти")');
    this.avatar = page.locator('[data-slot="avatar"], .avatar');
  }

  async goto() {
    await this.page.goto(`${this.baseUrl}/dashboard`);
    await this.page.waitForLoadState("networkidle");
  }

  async isOnDashboard(): Promise<boolean> {
    return this.page.url().includes("/dashboard");
  }

  async waitForDashboardLoad() {
    await this.welcomeTitle.waitFor({ state: "visible", timeout: 10000 });
  }

  async getUserEmailText(): Promise<string> {
    await this.userEmail.waitFor({ state: "visible", timeout: 5000 });
    const text = await this.userEmail.textContent();
    return text || "";
  }

  async clickSignOut() {
    await this.signOutButton.click();
  }

  async waitForSignOut() {
    // Wait for redirect to login page
    await this.page.waitForURL("**/login", { timeout: 10000 });
  }

  async verifyUserIsLoggedIn(email: string) {
    await this.waitForDashboardLoad();
    const userEmailText = await this.getUserEmailText();
    expect(userEmailText).toContain(email);
  }

  async performSignOut() {
    await this.clickSignOut();
    await this.waitForSignOut();
  }
}
