import { Page, Locator, expect } from "@playwright/test";

export class LoginPage {
  readonly page: Page;
  readonly baseUrl: string;
  readonly emailInput: Locator;
  readonly sendOtpButton: Locator;
  readonly otpInput: Locator;
  readonly verifyOtpButton: Locator;
  readonly messageAlert: Locator;
  readonly title: Locator;
  readonly resendOtpButton: Locator;

  constructor(page: Page, baseUrl: string) {
    this.page = page;
    this.baseUrl = baseUrl;
    this.emailInput = page.locator('input[type="email"]');
    this.sendOtpButton = page.locator(
      'button:has-text("Отправить магический код")',
    );
    this.otpInput = page.locator('[data-slot="input"]').first(); // InputOtp component
    this.verifyOtpButton = page.locator('button:has-text("Проверить и войти")');
    this.messageAlert = page
      .locator('text="Код отправлен"')
      .or(page.locator('[data-slot="base"]'))
      .or(page.locator(".chip")); // Multiple selectors for messages
    this.title = page.locator("h1, h2").first();
    this.resendOtpButton = page.locator(
      'button:has-text("Не получили код? Попробуйте снова")',
    );
  }

  async goto() {
    await this.page.goto(`${this.baseUrl}/login`);
    await this.page.waitForLoadState("networkidle");
  }

  async enterEmail(email: string) {
    await this.emailInput.fill(email);
  }

  async clickSendOtp() {
    await this.sendOtpButton.click();
  }

  async clickResendOtp() {
    await this.resendOtpButton.click();
  }

  async enterOtp(code: string) {
    // Wait for the OTP input to be visible first
    await this.page.waitForSelector('[data-slot="segment"]', {
      timeout: 10000,
    });

    // For InputOtp component, we need to type each digit into individual segments
    const segments = this.page.locator('[data-slot="segment"]');
    for (let i = 0; i < code.length && i < 6; i++) {
      await segments.nth(i).click();
      await segments.nth(i).fill(code[i]);
    }
  }

  async clickVerifyOtp() {
    await this.verifyOtpButton.click();
  }

  async getMessageText(): Promise<string> {
    try {
      await this.messageAlert.waitFor({ timeout: 5000 });
      return (await this.messageAlert.textContent()) || "";
    } catch {
      return "";
    }
  }

  async waitForSuccessMessage() {
    await expect(this.messageAlert).toContainText("успешно", {
      timeout: 10000,
    });
  }

  async waitForErrorMessage() {
    await expect(this.messageAlert).toBeVisible({ timeout: 5000 });
  }

  async waitForOtpSentMessage() {
    // Wait for the success message that appears in the Chip component
    await this.page.waitForSelector(
      'text="Код отправлен! Проверьте вашу почту."',
      {
        timeout: 10000,
      },
    );
  }

  async isOtpInputVisible(): Promise<boolean> {
    try {
      const segments = this.page.locator('[data-slot="segment"]');
      await segments.first().waitFor({ state: "visible", timeout: 5000 });
      return true;
    } catch {
      return false;
    }
  }

  async isOnLoginPage(): Promise<boolean> {
    return this.page.url().includes("/login");
  }

  async performFullLogin(email: string, otpCode: string) {
    await this.enterEmail(email);
    await this.clickSendOtp();
    await this.waitForOtpSentMessage();
    await this.enterOtp(otpCode);
    await this.clickVerifyOtp();
  }
}
