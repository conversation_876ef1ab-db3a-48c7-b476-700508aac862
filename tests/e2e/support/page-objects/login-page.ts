import { type Page, type Locator, expect } from "@playwright/test";

export class LoginPage {
  readonly page: Page;
  readonly baseUrl: string;
  readonly emailInput: Locator;
  readonly sendOtpButton: Locator;
  readonly otpInput: Locator;
  readonly verifyOtpButton: Locator;
  readonly messageAlert: Locator;
  readonly title: Locator;
  readonly resendOtpButton: Locator;

  constructor(page: Page, baseUrl: string) {
    this.page = page;
    this.baseUrl = baseUrl;
    this.emailInput = page.locator('input[type="email"]');
    this.sendOtpButton = page.locator(
      'button:has-text("Отправить магический код")',
    );
    this.otpInput = page.locator('[data-slot="input"]').first(); // InputOtp component
    this.verifyOtpButton = page.locator('button:has-text("Проверить и войти")');
    this.messageAlert = page
      .locator('text="Код отправлен"')
      .or(page.locator('[data-slot="base"]'))
      .or(page.locator(".chip")); // Multiple selectors for messages
    this.title = page.locator("h1, h2").first();
    this.resendOtpButton = page.locator(
      'button:has-text("Не получили код? Попробуйте снова")',
    );
  }

  async goto() {
    await this.page.goto(`${this.baseUrl}/login`);
    await this.page.waitForLoadState("networkidle");
  }

  async enterEmail(email: string) {
    await this.emailInput.fill(email);
  }

  async clickSendOtp() {
    await this.sendOtpButton.click();
  }

  async clickResendOtp() {
    await this.resendOtpButton.click();
  }

  async enterOtp(code: string) {
    // Wait for the OTP form to be visible first
    await this.page.waitForSelector('text="Введите код подтверждения"', {
      timeout: 10000,
    });

    // Find the OTP input field (HeroUI InputOtp creates a single input with maxlength=6)
    const otpInput = this.page.locator(
      'input[maxlength="6"][inputmode="numeric"]',
    );
    await otpInput.waitFor({ state: "visible", timeout: 5000 });

    // Click and fill the OTP input
    await otpInput.click();
    await otpInput.fill(code);
  }

  async clickVerifyOtp() {
    // Wait for the button to be enabled (it's disabled until OTP is fully entered)
    await this.verifyOtpButton.waitFor({ state: "visible", timeout: 5000 });

    // Check if button is enabled
    const isDisabled = await this.verifyOtpButton.isDisabled();

    if (isDisabled) {
      // Wait for button to be enabled
      await this.page.waitForFunction(
        () => {
          const button = document.querySelector(
            'button:has-text("Проверить и войти")',
          ) as HTMLButtonElement;
          return button && !button.disabled;
        },
        { timeout: 10000 },
      );
    }

    await this.verifyOtpButton.click();

    // Wait a moment for the response
    await this.page.waitForTimeout(1000);
  }

  async getMessageText(): Promise<string> {
    try {
      // Wait a moment for the message to appear
      await this.page.waitForTimeout(2000);

      // Look for error messages first
      const errorMessage = this.page.locator('text="Invalid or expired OTP"');
      if ((await errorMessage.count()) > 0) {
        return (await errorMessage.textContent()) || "";
      }

      // Fallback to Russian error message
      const russianError = this.page.locator('text="Неверный код"');
      if ((await russianError.count()) > 0) {
        return (await russianError.textContent()) || "";
      }

      // Fallback to success messages
      const successMessage = this.page.locator('text="Вход выполнен успешно!"');
      if ((await successMessage.count()) > 0) {
        return (await successMessage.textContent()) || "";
      }

      // Fallback to any chip message
      const messageChips = await this.page.locator('[data-slot="base"]').all();
      if (messageChips.length > 0) {
        const lastMessage = messageChips[messageChips.length - 1];
        return (await lastMessage.textContent()) || "";
      }

      return "";
    } catch {
      return "";
    }
  }

  async waitForSuccessMessage() {
    await expect(this.messageAlert).toContainText("успешно", {
      timeout: 10000,
    });
  }

  async waitForErrorMessage() {
    await expect(this.messageAlert).toBeVisible({ timeout: 5000 });
  }

  async waitForOtpSentMessage() {
    // Wait for the success message that appears in the Chip component
    await this.page.waitForSelector(
      'text="Код отправлен! Проверьте вашу почту."',
      {
        timeout: 10000,
      },
    );
  }

  async isOtpInputVisible(): Promise<boolean> {
    try {
      // Check if the OTP form is visible
      await this.page.waitForSelector('text="Введите код подтверждения"', {
        timeout: 5000,
      });
      return true;
    } catch {
      return false;
    }
  }

  async isOnLoginPage(): Promise<boolean> {
    return this.page.url().includes("/login");
  }

  async performFullLogin(email: string, otpCode: string) {
    await this.enterEmail(email);
    await this.clickSendOtp();
    await this.waitForOtpSentMessage();
    await this.enterOtp(otpCode);
    await this.clickVerifyOtp();
  }
}
