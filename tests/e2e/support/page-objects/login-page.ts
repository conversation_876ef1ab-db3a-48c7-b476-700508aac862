import { Page, Locator, expect } from '@playwright/test';

export class LoginPage {
  readonly page: Page;
  readonly emailInput: Locator;
  readonly sendOtpButton: Locator;
  readonly otpInput: Locator;
  readonly verifyOtpButton: Locator;
  readonly messageAlert: Locator;
  readonly title: Locator;

  constructor(page: Page) {
    this.page = page;
    this.emailInput = page.locator('input[type="email"]');
    this.sendOtpButton = page.locator('button:has-text("Отправить код")');
    this.otpInput = page.locator('input[placeholder*="код"], input[placeholder*="OTP"]');
    this.verifyOtpButton = page.locator('button:has-text("Войти")');
    this.messageAlert = page.locator('[role="alert"], .alert, .message');
    this.title = page.locator('h1, h2').first();
  }

  async goto() {
    await this.page.goto('/login');
    await this.page.waitForLoadState('networkidle');
  }

  async enterEmail(email: string) {
    await this.emailInput.fill(email);
  }

  async clickSendOtp() {
    await this.sendOtpButton.click();
  }

  async enterOtp(code: string) {
    await this.otpInput.fill(code);
  }

  async clickVerifyOtp() {
    await this.verifyOtpButton.click();
  }

  async getMessageText(): Promise<string> {
    try {
      await this.messageAlert.waitFor({ timeout: 5000 });
      return await this.messageAlert.textContent() || '';
    } catch {
      return '';
    }
  }

  async waitForSuccessMessage() {
    await expect(this.messageAlert).toContainText('успешно', { timeout: 10000 });
  }

  async waitForErrorMessage() {
    await expect(this.messageAlert).toBeVisible({ timeout: 5000 });
  }

  async waitForOtpSentMessage() {
    await expect(this.messageAlert).toContainText('отправлен', { timeout: 10000 });
  }

  async isOtpInputVisible(): Promise<boolean> {
    try {
      await this.otpInput.waitFor({ state: 'visible', timeout: 5000 });
      return true;
    } catch {
      return false;
    }
  }

  async isOnLoginPage(): Promise<boolean> {
    return this.page.url().includes('/login');
  }

  async performFullLogin(email: string, otpCode: string) {
    await this.enterEmail(email);
    await this.clickSendOtp();
    await this.waitForOtpSentMessage();
    await this.enterOtp(otpCode);
    await this.clickVerifyOtp();
  }
}
