import { setWorldConstructor, World, IWorldOptions } from "@cucumber/cucumber";
import { <PERSON><PERSON><PERSON>, BrowserContext, Page, chromium } from "@playwright/test";

export interface ISimpleWorld extends World {
  browser?: Browser;
  context?: BrowserContext;
  page?: Page;
  baseUrl: string;
  init(): Promise<void>;
  cleanup(): Promise<void>;
}

export class SimpleWorld extends World implements ISimpleWorld {
  browser?: Browser;
  context?: BrowserContext;
  page?: Page;
  baseUrl: string;

  constructor(options: IWorldOptions) {
    super(options);
    this.baseUrl = options.parameters?.baseUrl || "http://localhost:3001";
  }

  async init() {
    this.browser = await chromium.launch({
      headless: process.env.CI === "true",
      slowMo: process.env.CI === "true" ? 0 : 100,
    });
    this.context = await this.browser.newContext({
      viewport: { width: 1280, height: 720 },
      ignoreHTTPSErrors: true,
    });
    this.page = await this.context.newPage();
  }

  async cleanup() {
    if (this.context) {
      await this.context.close();
    }
    if (this.browser) {
      await this.browser.close();
    }
  }
}

setWorldConstructor(SimpleWorld);
