import {
  setWorldConstructor,
  World,
  type IWorldOptions,
} from "@cucumber/cucumber";
import {
  type Browser,
  type BrowserContext,
  type Page,
  chromium,
} from "@playwright/test";
import { LoginPage } from "./page-objects/login-page";
import { DashboardPage } from "./page-objects/dashboard-page";

export interface ICustomWorld extends World {
  browser?: Browser;
  context?: BrowserContext;
  page?: Page;
  loginPage?: LoginPage;
  dashboardPage?: DashboardPage;
  baseUrl: string;
  testEmail?: string;
  otpCode?: string;
  init(): Promise<void>;
  cleanup(): Promise<void>;
  pickle?: {
    name?: string;
  };
}

export class CustomWorld extends World implements ICustomWorld {
  browser?: Browser;
  context?: BrowserContext;
  page?: Page;
  loginPage?: LoginPage;
  dashboardPage?: DashboardPage;
  baseUrl: string;
  testEmail?: string;
  otpCode?: string;

  constructor(options: IWorldOptions) {
    super(options);
    this.baseUrl = options.parameters?.baseUrl || "http://localhost:3001";
  }

  async init() {
    this.browser = await chromium.launch({
      headless: process.env.CI === "true",
      slowMo: process.env.CI === "true" ? 0 : 100,
    });
    this.context = await this.browser.newContext({
      viewport: { width: 1280, height: 720 },
      ignoreHTTPSErrors: true,
    });
    this.page = await this.context.newPage();

    // Initialize page objects
    this.loginPage = new LoginPage(this.page);
    this.dashboardPage = new DashboardPage(this.page);
  }

  async cleanup() {
    if (this.context) {
      await this.context.close();
    }
    if (this.browser) {
      await this.browser.close();
    }
  }
}

setWorldConstructor(CustomWorld);
