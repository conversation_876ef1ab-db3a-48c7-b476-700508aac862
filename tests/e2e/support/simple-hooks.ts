import { Before, After } from "@cucumber/cucumber";
import { ISimpleWorld } from "./simple-world";

Before(async function (this: ISimpleWorld) {
  await this.init();
  console.log("🎬 Starting scenario");
});

After(async function (this: ISimpleWorld, scenario) {
  if (scenario.result?.status === "FAILED" && this.page) {
    const screenshot = await this.page.screenshot({
      path: `test-results/screenshots/scenario-${Date.now()}.png`,
      fullPage: true,
    });
    this.attach(screenshot, "image/png");
  }

  await this.cleanup();
  console.log(`🏁 Finished scenario - ${scenario.result?.status}`);
});
