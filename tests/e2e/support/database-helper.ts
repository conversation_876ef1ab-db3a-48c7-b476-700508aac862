import { prisma } from "../../../src/lib/prisma";

export class DatabaseHelper {
  async connect() {
    await prisma.$connect();
    console.log("📊 Connected to test database");
  }

  async disconnect() {
    await prisma.$disconnect();
    console.log("📊 Disconnected from test database");
  }

  async cleanDatabase() {
    // Clean all tables in the correct order (respecting foreign key constraints)
    await prisma.otp.deleteMany();
    await prisma.user.deleteMany();
  }

  async createUser(email: string) {
    return await prisma.user.create({
      data: { email },
    });
  }

  async createOtp(email: string, code: string, expiresAt: Date) {
    return await prisma.otp.create({
      data: {
        email,
        code,
        expiresAt,
      },
    });
  }

  async findUserByEmail(email: string) {
    return await prisma.user.findUnique({
      where: { email },
    });
  }

  async findOtpByEmail(email: string) {
    return await prisma.otp.findFirst({
      where: { email },
      orderBy: { id: "desc" },
    });
  }

  async getValidOtp(email: string) {
    return await prisma.otp.findFirst({
      where: {
        email,
        expiresAt: { gt: new Date() },
      },
      orderBy: { id: "desc" },
    });
  }

  async countUsers() {
    return await prisma.user.count();
  }

  async countOtps() {
    return await prisma.otp.count();
  }
}
