import  { PrismaClient } from "../../../src/generated/prisma/client";
// import { prisma } from "../../../src/lib/prisma";

export class DatabaseHelper {
  private prisma!: PrismaClient;

  async connect() {
    // Dynamic import to avoid ES module issues
    // const { PrismaClient: PrismaClientClass } = await import(
    //   "../../../src/generated/prisma"
    // );
    this.prisma = new PrismaClient({
      datasources: {
        db: {
          url: process.env.DATABASE_URL, // This will use the test DATABASE_URL from .env.test
        },
      },
    });

    await this.prisma.$connect();
    console.log("📊 Connected to test database");
  }

  async disconnect() {
    await this.prisma.$disconnect();
    console.log("📊 Disconnected from test database");
  }

  async cleanDatabase() {
    // Clean all tables in the correct order (respecting foreign key constraints)
    await this.prisma.otp.deleteMany();
    await this.prisma.user.deleteMany();
  }

  async createUser(email: string) {
    return await this.prisma.user.create({
      data: { email },
    });
  }

  async createOtp(email: string, code: string, expiresAt: Date) {
    return await this.prisma.otp.create({
      data: {
        email,
        code,
        expiresAt,
      },
    });
  }

  async findUserByEmail(email: string) {
    return await this.prisma.user.findUnique({
      where: { email },
    });
  }

  async findOtpByEmail(email: string) {
    return await this.prisma.otp.findFirst({
      where: { email },
      orderBy: { id: "desc" },
    });
  }

  async getValidOtp(email: string) {
    return await this.prisma.otp.findFirst({
      where: {
        email,
        expiresAt: { gt: new Date() },
      },
      orderBy: { id: "desc" },
    });
  }

  async countUsers() {
    return await this.prisma.user.count();
  }

  async countOtps() {
    return await this.prisma.otp.count();
  }
}
