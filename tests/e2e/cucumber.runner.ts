#!/usr/bin/env ts-node

import { runCucumber } from '@cucumber/cucumber/api';
import config from './config/cucumber.config';

async function main() {
  const { success } = await runCucumber({
    ...config,
    sources: {
      paths: ['tests/e2e/features/**/*.feature'],
      defaultDialect: 'en',
      names: [],
      tagExpression: '',
    },
    support: {
      requireModules: ['ts-node/register'],
      requirePaths: [
        'tests/e2e/support/**/*.ts',
        'tests/e2e/step-definitions/**/*.ts'
      ],
      importPaths: []
    }
  });

  process.exit(success ? 0 : 1);
}

if (require.main === module) {
  main().catch(console.error);
}
