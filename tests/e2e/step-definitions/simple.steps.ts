import { Given, When, Then } from "@cucumber/cucumber";
import { expect } from "@playwright/test";
import { ISimpleWorld } from "../support/simple-world";

Given("the application is running", async function (this: ISimpleWorld) {
  expect(this.page).toBeDefined();
});

When("I navigate to the home page", async function (this: ISimpleWorld) {
  if (!this.page) throw new Error("Page not initialized");
  await this.page.goto(this.baseUrl);
});

Then("I should see the application", async function (this: ISimpleWorld) {
  if (!this.page) throw new Error("Page not initialized");
  await expect(this.page.locator("body")).toBeVisible();
});
