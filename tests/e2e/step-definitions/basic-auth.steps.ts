import { Given, When, Then } from '@cucumber/cucumber';
import { expect } from '@playwright/test';
import { ICustomWorld } from '../support/world';
import { databaseHelper } from '../support/hooks';

// Background steps
Given('the application is running', async function (this: ICustomWorld) {
  expect(this.page).toBeDefined();
});

Given('the database is clean', async function (this: ICustomWorld) {
  await databaseHelper.cleanDatabase();
});

// Navigation steps
Given('I am on the login page', async function (this: ICustomWorld) {
  if (!this.loginPage) throw new Error("Login page not initialized");
  await this.loginPage.goto();
});

When('I navigate to the login page', async function (this: ICustomWorld) {
  if (!this.loginPage) throw new Error("Login page not initialized");
  await this.loginPage.goto();
});

// Form interaction steps
When('I enter email {string}', async function (this: ICustomWorld, email: string) {
  if (!this.loginPage) throw new Error("Login page not initialized");
  await this.loginPage.enterEmail(email);
  this.testEmail = email;
});

When('I click send OTP button', async function (this: ICustomWorld) {
  if (!this.loginPage) throw new Error("Login page not initialized");
  await this.loginPage.clickSendOtp();
});

// Assertion steps
Then('I should see the login form', async function (this: ICustomWorld) {
  if (!this.loginPage) throw new Error("Login page not initialized");
  expect(await this.loginPage.isOnLoginPage()).toBe(true);
  await expect(this.page!.locator('input[type="email"]')).toBeVisible();
  await expect(this.page!.locator('button:has-text("Отправить магический код")')).toBeVisible();
});

Then('I should see OTP sent message', async function (this: ICustomWorld) {
  if (!this.loginPage) throw new Error("Login page not initialized");
  await this.loginPage.waitForOtpSentMessage();
});
