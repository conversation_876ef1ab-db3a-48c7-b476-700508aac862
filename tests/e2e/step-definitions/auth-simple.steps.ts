import { Given, When, Then } from "@cucumber/cucumber";
import { expect } from "@playwright/test";
import { ISimpleWorld } from "../support/simple-world";

// Navigation steps
Given("I am on the login page", async function (this: ISimpleWorld) {
  if (!this.page) throw new Error("Page not initialized");
  await this.page.goto(`${this.baseUrl}/login`);
});

When("I navigate to the login page", async function (this: ISimpleWorld) {
  if (!this.page) throw new Error("Page not initialized");
  await this.page.goto(`${this.baseUrl}/login`);
});

// Form interaction steps
When(
  "I enter email {string}",
  async function (this: ISimpleWorld, email: string) {
    if (!this.page) throw new Error("Page not initialized");
    await this.page.fill('input[type="email"]', email);
  },
);

When("I click send OTP button", async function (this: ISimpleWorld) {
  if (!this.page) throw new Error("Page not initialized");
  await this.page.click('button:has-text("Отправить магический код")');
});

// Assertion steps
Then("I should see the login form", async function (this: ISimpleWorld) {
  if (!this.page) throw new Error("Page not initialized");
  await expect(this.page.locator('input[type="email"]')).toBeVisible();
  await expect(
    this.page.locator('button:has-text("Отправить магический код")'),
  ).toBeVisible();
});

Then("I should see OTP sent message", async function (this: ISimpleWorld) {
  if (!this.page) throw new Error("Page not initialized");
  // Wait for the message to appear
  await expect(this.page.locator("text=/отправлен/i")).toBeVisible({
    timeout: 10000,
  });
});
