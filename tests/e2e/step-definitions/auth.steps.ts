import { Given, When, Then } from '@cucumber/cucumber';
import { expect } from '@playwright/test';
import { ICustomWorld } from '../support/world';
import { databaseHelper, mailHelper } from '../support/hooks';

// Background steps
Given('the application is running', async function (this: ICustomWorld) {
  // Application should be running via webServer in playwright config
  expect(this.page).toBeDefined();
});

Given('the database is clean', async function (this: ICustomWorld) {
  await databaseHelper.cleanDatabase();
});

// Navigation steps
Given('I am on the login page', async function (this: ICustomWorld) {
  await this.loginPage!.goto();
  expect(await this.loginPage!.isOnLoginPage()).toBe(true);
});

Given('I am not logged in', async function (this: ICustomWorld) {
  // Clear any existing cookies/session
  await this.context!.clearCookies();
});

Given('I am logged in as {string}', async function (this: ICustomWorld, email: string) {
  await this.loginPage!.goto();
  await this.loginPage!.enterEmail(email);
  await this.loginPage!.clickSendOtp();
  await this.loginPage!.waitForOtpSentMessage();
  
  // Get OTP from email
  const otpCode = await mailHelper.waitForOtp(email, 10000);
  expect(otpCode).toBeTruthy();
  
  await this.loginPage!.enterOtp(otpCode!);
  await this.loginPage!.clickVerifyOtp();
  await this.page!.waitForURL('**/dashboard', { timeout: 10000 });
  
  this.testEmail = email;
});

// Email input steps
When('I enter email {string}', async function (this: ICustomWorld, email: string) {
  await this.loginPage!.enterEmail(email);
  this.testEmail = email;
});

// OTP request steps
When('I click send OTP button', async function (this: ICustomWorld) {
  await this.loginPage!.clickSendOtp();
});

When('I click send OTP button again', async function (this: ICustomWorld) {
  await this.loginPage!.clickSendOtp();
});

// OTP verification steps
When('I enter the received OTP code', async function (this: ICustomWorld) {
  expect(this.testEmail).toBeDefined();
  const otpCode = await mailHelper.waitForOtp(this.testEmail!, 10000);
  expect(otpCode).toBeTruthy();
  
  await this.loginPage!.enterOtp(otpCode!);
  this.otpCode = otpCode!;
});

When('I enter the expired OTP code', async function (this: ICustomWorld) {
  expect(this.otpCode).toBeDefined();
  await this.loginPage!.enterOtp(this.otpCode!);
});

When('I enter OTP code {string}', async function (this: ICustomWorld, code: string) {
  await this.loginPage!.enterOtp(code);
});

When('I click verify OTP button', async function (this: ICustomWorld) {
  await this.loginPage!.clickVerifyOtp();
});

// Timing steps
When('I wait for OTP to expire', async function (this: ICustomWorld) {
  // Wait for OTP expiration (1 minute + buffer)
  await this.page!.waitForTimeout(65000);
});

When('I wait for {int} seconds', async function (this: ICustomWorld, seconds: number) {
  await this.page!.waitForTimeout(seconds * 1000);
});

When('I refresh the page', async function (this: ICustomWorld) {
  await this.page!.reload();
  await this.page!.waitForLoadState('networkidle');
});

// Dashboard steps
When('I am on the dashboard page', async function (this: ICustomWorld) {
  await this.dashboardPage!.goto();
});

When('I try to access dashboard directly', async function (this: ICustomWorld) {
  await this.page!.goto('/dashboard');
});

When('I click sign out button', async function (this: ICustomWorld) {
  await this.dashboardPage!.clickSignOut();
});

// Assertion steps
Then('I should see OTP sent message', async function (this: ICustomWorld) {
  await this.loginPage!.waitForOtpSentMessage();
});

Then('I should see an error message', async function (this: ICustomWorld) {
  await this.loginPage!.waitForErrorMessage();
});

Then('I should see {string} error message', async function (this: ICustomWorld, errorText: string) {
  const message = await this.loginPage!.getMessageText();
  expect(message).toContain(errorText);
});

Then('I should be redirected to dashboard', async function (this: ICustomWorld) {
  await this.page!.waitForURL('**/dashboard', { timeout: 10000 });
  expect(await this.dashboardPage!.isOnDashboard()).toBe(true);
});

Then('I should be redirected to login page', async function (this: ICustomWorld) {
  await this.page!.waitForURL('**/login', { timeout: 10000 });
  expect(await this.loginPage!.isOnLoginPage()).toBe(true);
});

Then('I should see welcome message with {string}', async function (this: ICustomWorld, email: string) {
  await this.dashboardPage!.verifyUserIsLoggedIn(email);
});

Then('I should still be on dashboard', async function (this: ICustomWorld) {
  expect(await this.dashboardPage!.isOnDashboard()).toBe(true);
});

Then('I should not be able to access dashboard without login', async function (this: ICustomWorld) {
  await this.page!.goto('/dashboard');
  await this.page!.waitForURL('**/login', { timeout: 10000 });
  expect(await this.loginPage!.isOnLoginPage()).toBe(true);
});

// Email verification steps
Then('an OTP email should be sent to {string}', async function (this: ICustomWorld, email: string) {
  const emailMessage = await mailHelper.waitForEmail(email, 10000);
  expect(emailMessage).toBeTruthy();
  expect(emailMessage!.subject).toContain('OTP');
});

Then('the latest OTP should be valid for {string}', async function (this: ICustomWorld, email: string) {
  const otpCode = await mailHelper.getOtpForEmail(email);
  expect(otpCode).toBeTruthy();
  expect(otpCode).toMatch(/^\d{6}$/);
});

// Configuration steps
Given('the OTP expiration is set to {int} minute', async function (this: ICustomWorld, minutes: number) {
  // This is configured via environment variables
  expect(process.env.OTP_EXPIRATION_MINUTES).toBe(minutes.toString());
});

When('I request OTP for {string}', async function (this: ICustomWorld, email: string) {
  await this.loginPage!.goto();
  await this.loginPage!.enterEmail(email);
  await this.loginPage!.clickSendOtp();
  await this.loginPage!.waitForOtpSentMessage();
  
  this.testEmail = email;
  this.otpCode = await mailHelper.waitForOtp(email, 10000);
});

Then('the OTP should be expired', async function (this: ICustomWorld) {
  expect(this.testEmail).toBeDefined();
  const validOtp = await databaseHelper.getValidOtp(this.testEmail!);
  expect(validOtp).toBeNull();
});

Then('login with expired OTP should fail', async function (this: ICustomWorld) {
  expect(this.otpCode).toBeDefined();
  await this.loginPage!.enterOtp(this.otpCode!);
  await this.loginPage!.clickVerifyOtp();
  
  const message = await this.loginPage!.getMessageText();
  expect(message).toContain('Invalid or expired OTP');
});
