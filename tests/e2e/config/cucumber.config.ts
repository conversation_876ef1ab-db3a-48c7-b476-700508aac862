module.exports = {
  default: {
    requireModule: ["ts-node/register"],
    require: [
      "tests/e2e/support/**/*.ts",
      "tests/e2e/step-definitions/**/*.ts",
    ],
    format: [
      "progress-bar",
      "json:test-results/cucumber-report.json",
      "html:test-results/cucumber-report.html",
      "@cucumber/pretty-formatter",
    ],
    formatOptions: {
      snippetInterface: "async-await",
    },
    publishQuiet: true,
    dryRun: false,
    failFast: false,
    strict: true,
    worldParameters: {
      baseUrl: process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3001",
    },
  },
};
