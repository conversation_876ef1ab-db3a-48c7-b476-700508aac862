Feature: Authentication
  As a user
  I want to authenticate using OTP
  So that I can access the application securely

  Background:
    Given the application is running
    And the database is clean

  Scenario: Successful OTP login flow
    Given I am on the login page
    When I enter email "<EMAIL>"
    And I click send OTP button
    Then I should see <PERSON><PERSON> sent message
    And an OTP email should be sent to "<EMAIL>"
    When I enter the received OTP code
    And I click verify OTP button
    Then I should be redirected to dashboard
    And I should see welcome message with "<EMAIL>"

  Scenario: Login with invalid email format
    Given I am on the login page
    When I enter email "invalid-email"
    And I click send OTP button
    Then I should see an error message

  Sc<PERSON><PERSON>: Login with expired OTP
    Given I am on the login page
    And I enter email "<EMAIL>"
    And I click send OTP button
    And I should see <PERSON><PERSON> sent message
    When I wait for <PERSON><PERSON> to expire
    And I enter the expired OTP code
    And I click verify OTP button
    Then I should see "Invalid or expired OTP" error message

  Sc<PERSON>rio: Login with incorrect OTP
    Given I am on the login page
    When I enter email "<EMAIL>"
    And I click send OTP button
    Then I should see <PERSON><PERSON> sent message
    When I enter OTP code "123456"
    And I click verify <PERSON>TP button
    Then I should see "Invalid or expired OTP" error message

  Sc<PERSON>rio: Multiple OTP requests for same email
    Given I am on the login page
    When I enter email "<EMAIL>"
    And I click send OTP button
    Then I should see <PERSON><PERSON> sent message
    When I click send OTP button again
    Then I should see OTP sent message
    And the latest OTP should be valid for "<EMAIL>"

  Scenario: User logout
    Given I am logged in as "<EMAIL>"
    When I am on the dashboard page
    And I click sign out button
    Then I should be redirected to login page
    And I should not be able to access dashboard without login

  Scenario: Protected route access without authentication
    Given I am not logged in
    When I try to access dashboard directly
    Then I should be redirected to login page

  Scenario: Session persistence
    Given I am logged in as "<EMAIL>"
    When I refresh the page
    Then I should still be on dashboard
    And I should see welcome message with "<EMAIL>"

  Scenario: OTP expiration configuration
    Given the OTP expiration is set to 5 seconds
    When I request OTP for "<EMAIL>"
    And I wait for 6 seconds
    Then the OTP should be expired
    And login with expired OTP should fail
