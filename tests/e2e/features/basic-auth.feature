Feature: Basic Authentication
  As a user
  I want to authenticate using OTP
  So that I can access the application securely

  Background:
    Given the application is running
    And the database is clean

  Scenario: Navigate to login page
    When I navigate to the login page
    Then I should see the login form

  Scenario: Request OTP with valid email
    Given I am on the login page
    When I enter email "<EMAIL>"
    And I click send OTP button
    Then I should see OTP sent message
