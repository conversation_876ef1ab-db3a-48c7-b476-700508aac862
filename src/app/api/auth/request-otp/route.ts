import { NextResponse } from "next/server";
import nodemailer from "nodemailer";
import { prisma } from "@/lib/prisma";

export async function POST(req: Request) {
  const { email } = await req.json();

  const code = Math.floor(100000 + Math.random() * 900000).toString();

  const expirationMinutes = Number.parseInt(
    process.env.OTP_EXPIRATION_MINUTES || "10",
  );
  const expiresAt = new Date(Date.now() + expirationMinutes * 60 * 1000);

  await prisma.otp.create({
    data: {
      email,
      code,
      expiresAt,
    },
  });

  const transporter = nodemailer.createTransport({
    host: process.env.EMAIL_HOST,
    port: parseInt(process.env.EMAIL_PORT || "1025"),
  });

  await transporter.sendMail({
    from: process.env.EMAIL_FROM,
    to: email,
    subject: "Your OTP Code",
    text: `Your OTP code is ${code}`,
  });

  return NextResponse.json({ message: "OTP sent" });
}
